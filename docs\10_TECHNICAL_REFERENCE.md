# ERDB AI-Powered Knowledge Management Platform - Technical Reference

## Table of Contents

1. [System Architecture & Components](#1-system-architecture--components)
2. [Advanced Features Technical Specifications](#2-advanced-features-technical-specifications)
3. [Configuration & Deployment Technical Guide](#3-configuration--deployment-technical-guide)
4. [Complete API Reference & Developer Integration](#4-complete-api-reference--developer-integration)
5. [Technical Troubleshooting & System Maintenance](#5-technical-troubleshooting--system-maintenance)

---

## 1. System Architecture & Components

### 1.1 Flask Application Server Architecture

**Core Architecture:**
```
┌─────────────────────────────────────────────────────────────────┐
│                    Flask Application Server                     │
├─────────────────────────────────────────────────────────────────┤
│  • Optimized routing with streaming response capabilities       │
│  • Session management with audit trails                        │
│  • CSRF protection and rate limiting                           │
│  • Real-time table rendering and scientific name processing    │
│  • Gated download system with token-based security            │
└─────────────────────────────────────────────────────────────────┘
                                  │
                                  ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Service Layer                              │
├─────────────────────────────────────────────────────────────────┤
│  Query Service │ Document Service │ Citation Service │ Auth     │
│  Table Service │ Scientific Names │ Download Service │ Analytics│
└─────────────────────────────────────────────────────────────────┘
```

**Technical Specifications:**
- **Framework**: Flask 2.3+ with Werkzeug WSGI server
- **Concurrency**: Thread-based request handling (configurable workers)
- **Session Management**: Server-side sessions with Redis backend (optional)
- **Security**: CSRF tokens, rate limiting (100 req/min general, 20 req/min chat)
- **Streaming**: Server-Sent Events (SSE) for real-time response delivery
- **File Handling**: 25MB upload limit with multipart processing

**Key Components:**
```python
# Core Flask configuration
app.config.update({
    'MAX_CONTENT_LENGTH': 25 * 1024 * 1024,  # 25MB
    'SECRET_KEY': os.getenv('FLASK_SECRET_KEY'),
    'WTF_CSRF_ENABLED': True,
    'RATELIMIT_STORAGE_URL': 'memory://',
    'UPLOAD_FOLDER': os.getenv('TEMP_FOLDER', './_temp')
})
```

### 1.2 Ollama AI Integration with GPU Layer Offloading

**Architecture Overview:**
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Flask App     │───▶│  Ollama Service  │───▶│   GPU Layers    │
│                 │    │  (Port 11434)    │    │   (Offloaded)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Model Config   │    │  Parameter       │    │  Performance    │
│  Management     │    │  Optimization    │    │  Monitoring     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

**GPU Layer Offloading Configurations:**
```json
{
  "gpu_layer_offloading": {
    "conservative": {
      "num_gpu": 25,
      "description": "Maximum VRAM savings, slower processing",
      "vram_usage": "~4GB",
      "performance": "70% of full GPU"
    },
    "balanced": {
      "num_gpu": 35,
      "description": "Good speed/memory balance",
      "vram_usage": "~6GB",
      "performance": "85% of full GPU"
    },
    "performance": {
      "num_gpu": 45,
      "description": "Maximum speed, higher VRAM usage",
      "vram_usage": "~8GB",
      "performance": "95% of full GPU"
    },
    "disabled": {
      "num_gpu": 0,
      "description": "Full GPU usage, maximum VRAM",
      "vram_usage": "~12GB+",
      "performance": "100% GPU utilization"
    }
  }
}
```

**Model Configuration Management:**
```python
# Dynamic model parameter application
def apply_model_parameters(mode: str, custom_params: dict = None):
    base_params = {
        "strict": {
            "temperature": 0.2,
            "num_ctx": 4096,
            "num_predict": 1536,
            "top_p": 0.9,
            "top_k": 60,
            "repeat_penalty": 1.1
        },
        "balanced": {
            "temperature": 0.25,
            "num_ctx": 4096,
            "num_predict": 1536,
            "top_p": 0.9,
            "top_k": 60,
            "repeat_penalty": 1.1
        },
        "off": {
            "temperature": 0.8,
            "num_ctx": 4096,
            "num_predict": 1280,
            "top_p": 0.95,
            "top_k": 80,
            "repeat_penalty": 1.0
        }
    }
    
    params = base_params.get(mode, base_params["strict"])
    if custom_params:
        params.update(custom_params)
    
    return params
```

### 1.3 Unified ChromaDB Vector Database Architecture

**Database Schema:**
```
┌─────────────────────────────────────────────────────────────────┐
│                    Unified ChromaDB Collection                  │
├─────────────────────────────────────────────────────────────────┤
│  Document ID │ Vector Embedding │ Metadata │ Content │ Tables   │
├─────────────────────────────────────────────────────────────────┤
│  doc_001     │ [0.1, 0.2, ...]  │ {...}    │ "text"  │ [{...}] │
│  doc_002     │ [0.3, 0.4, ...]  │ {...}    │ "text"  │ [{...}] │
└─────────────────────────────────────────────────────────────────┘
```

**Metadata Structure:**
```json
{
  "document_id": "doc_marine_2023_001",
  "filename": "marine_biodiversity_study_2023.pdf",
  "category": "marine-research",
  "page_number": 15,
  "chunk_index": 23,
  "publication_date": "2023-11-15T00:00:00Z",
  "upload_timestamp": "2024-01-15T09:30:00Z",
  "content_type": "text",
  "has_tables": true,
  "table_count": 3,
  "scientific_names": ["*Pterocarpus indicus*", "*Acropora cervicornis*"],
  "gated_download": true,
  "source_url": "https://example.com/study.pdf"
}
```

**Temporal Indexing Implementation:**
```python
# Recency bias calculation
def calculate_recency_score(publication_date: datetime, bias_weight: float = 0.0):
    if bias_weight == 0.0:
        return 1.0
    
    days_old = (datetime.now() - publication_date).days
    max_age_days = 365 * 5  # 5 years
    
    # Exponential decay with configurable bias
    recency_factor = math.exp(-days_old / (max_age_days * (1 - bias_weight)))
    return max(0.1, recency_factor)  # Minimum 0.1 to avoid zero scores

# MMR (Maximal Marginal Relevance) implementation
def apply_mmr_reranking(documents: List[dict], query_embedding: List[float], 
                       lambda_param: float = 0.5, fetch_k: int = 48):
    """
    Apply MMR to balance relevance and diversity
    lambda_param: 0.0 = max diversity, 1.0 = max relevance
    """
    selected = []
    remaining = documents[:fetch_k]
    
    while remaining and len(selected) < 12:
        mmr_scores = []
        for doc in remaining:
            relevance = cosine_similarity(query_embedding, doc['embedding'])
            
            if not selected:
                diversity = 0
            else:
                max_similarity = max([
                    cosine_similarity(doc['embedding'], sel['embedding']) 
                    for sel in selected
                ])
                diversity = max_similarity
            
            mmr_score = lambda_param * relevance - (1 - lambda_param) * diversity
            mmr_scores.append((mmr_score, doc))
        
        best_doc = max(mmr_scores, key=lambda x: x[0])[1]
        selected.append(best_doc)
        remaining.remove(best_doc)
    
    return selected
```

### 1.4 ScispaCy Microservice Pipeline

**Microservice Architecture:**
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Main App      │───▶│  ScispaCy        │───▶│   CRAFT Model   │
│   (Python 3.8) │    │  Microservice    │    │   Processing    │
│                 │    │  (Python 3.10)  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Regex Fallback │    │  Health Monitor  │    │  Confidence     │
│  Processing     │    │  (Port 5005)     │    │  Scoring        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

**Microservice Implementation:**
```python
# scripts/venv310_scispacy_microservice.py
import spacy
from flask import Flask, request, jsonify
import re
from typing import List, Dict, Tuple

app = Flask(__name__)

# Load ScispaCy CRAFT model
try:
    nlp = spacy.load("en_ner_craft_md")
    model_loaded = True
except OSError:
    nlp = None
    model_loaded = False

@app.route("/health", methods=["GET"])
def health():
    return jsonify({
        "status": "ok" if model_loaded else "error",
        "model_loaded": model_loaded,
        "port": 5005,
        "version": "1.0.0"
    })

@app.route("/detect_scientific_names", methods=["POST"])
def detect_scientific_names():
    data = request.get_json()
    text = data.get("text", "")
    preserve_urls = data.get("preserve_urls", True)
    
    if not model_loaded:
        return jsonify({"error": "ScispaCy model not loaded"}), 500
    
    # Process with ScispaCy
    doc = nlp(text)
    scientific_names = []
    
    for ent in doc.ents:
        if ent.label_ in ["SPECIES", "GENUS_SPECIES"]:
            scientific_names.append({
                "text": ent.text,
                "start": ent.start_char,
                "end": ent.end_char,
                "confidence": float(ent._.confidence) if hasattr(ent._, 'confidence') else 0.9,
                "method": "scispacy"
            })
    
    # Apply italicization while preserving URLs
    processed_text = apply_italicization(text, scientific_names, preserve_urls)
    
    return jsonify({
        "processed_text": processed_text,
        "scientific_names_found": scientific_names,
        "processing_method": "scispacy",
        "model_version": "en_ner_craft_md-0.5.1"
    })

def apply_italicization(text: str, names: List[Dict], preserve_urls: bool) -> str:
    """Apply markdown italicization while preserving URLs"""
    if preserve_urls:
        # Extract URLs to preserve them
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+'
        urls = re.findall(url_pattern, text)
        url_placeholders = {url: f"__URL_{i}__" for i, url in enumerate(urls)}
        
        # Replace URLs with placeholders
        for url, placeholder in url_placeholders.items():
            text = text.replace(url, placeholder)
    
    # Sort names by position (reverse order to avoid offset issues)
    names_sorted = sorted(names, key=lambda x: x['start'], reverse=True)
    
    # Apply italicization
    for name in names_sorted:
        original = name['text']
        italicized = f"*{original}*"
        text = text[:name['start']] + italicized + text[name['end']:]
    
    if preserve_urls:
        # Restore URLs
        for url, placeholder in url_placeholders.items():
            text = text.replace(placeholder, url)
    
    return text

if __name__ == "__main__":
    app.run(host="0.0.0.0", port=5005, debug=False)
```

### 1.5 PyMuPDF Table Extraction System

**Table Processing Pipeline:**
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   PDF Upload    │───▶│  PyMuPDF Table   │───▶│  Structure      │
│                 │    │  Detection       │    │  Analysis       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Content        │    │  Bootstrap HTML  │    │  Streaming      │
│  Embedding      │    │  Rendering       │    │  Display        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

**Advanced Table Detection Implementation:**
```python
import fitz  # PyMuPDF
from typing import List, Dict, Any

class AdvancedTableExtractor:
    def __init__(self, strategy: str = "advanced"):
        self.strategy = strategy
        self.min_table_rows = 2
        self.min_table_cols = 2
        
    def extract_tables_from_pdf(self, pdf_path: str) -> List[Dict[str, Any]]:
        """Extract tables using advanced PyMuPDF detection"""
        doc = fitz.open(pdf_path)
        tables = []
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # Find tables using PyMuPDF's table detection
            page_tables = page.find_tables(
                strategy=self.strategy,
                add_lines=True,
                snap_tolerance=3,
                join_tolerance=3
            )
            
            for table_index, table in enumerate(page_tables):
                table_data = self._process_table(table, page_num, table_index)
                if table_data:
                    tables.append(table_data)
        
        doc.close()
        return tables
    
    def _process_table(self, table, page_num: int, table_index: int) -> Dict[str, Any]:
        """Process individual table with structure preservation"""
        try:
            # Extract table data
            table_data = table.extract()
            
            if not self._validate_table(table_data):
                return None
            
            # Generate HTML with Bootstrap styling
            html_table = self._generate_html_table(table_data)
            
            # Detect table title (look for text above table)
            title = self._detect_table_title(table, page_num)
            
            return {
                "table_id": f"table_{page_num}_{table_index}",
                "page": page_num + 1,
                "title": title,
                "data": table_data,
                "html": html_table,
                "rows": len(table_data),
                "cols": len(table_data[0]) if table_data else 0,
                "extraction_method": "pymupdf_advanced",
                "bbox": table.bbox  # Bounding box coordinates
            }
        except Exception as e:
            print(f"Error processing table: {e}")
            return None
    
    def _validate_table(self, table_data: List[List[str]]) -> bool:
        """Validate table meets minimum requirements"""
        if not table_data or len(table_data) < self.min_table_rows:
            return False
        
        if not all(len(row) >= self.min_table_cols for row in table_data):
            return False
        
        # Check for meaningful content (not all empty cells)
        non_empty_cells = sum(1 for row in table_data for cell in row if cell.strip())
        total_cells = sum(len(row) for row in table_data)
        
        return non_empty_cells / total_cells > 0.3  # At least 30% non-empty
    
    def _generate_html_table(self, table_data: List[List[str]]) -> str:
        """Generate Bootstrap-styled HTML table"""
        if not table_data:
            return ""
        
        html = ['<table class="table table-striped table-bordered table-hover">']
        
        # Header row
        if len(table_data) > 1:
            html.append('<thead class="table-dark">')
            html.append('<tr>')
            for cell in table_data[0]:
                html.append(f'<th>{self._escape_html(cell)}</th>')
            html.append('</tr>')
            html.append('</thead>')
            
            # Body rows
            html.append('<tbody>')
            for row in table_data[1:]:
                html.append('<tr>')
                for cell in row:
                    html.append(f'<td>{self._escape_html(cell)}</td>')
                html.append('</tr>')
            html.append('</tbody>')
        else:
            # Single row table
            html.append('<tbody>')
            html.append('<tr>')
            for cell in table_data[0]:
                html.append(f'<td>{self._escape_html(cell)}</td>')
            html.append('</tr>')
            html.append('</tbody>')
        
        html.append('</table>')
        return ''.join(html)
    
    def _escape_html(self, text: str) -> str:
        """Escape HTML special characters"""
        return (text.replace('&', '&amp;')
                   .replace('<', '&lt;')
                   .replace('>', '&gt;')
                   .replace('"', '&quot;')
                   .replace("'", '&#x27;'))
    
    def _detect_table_title(self, table, page_num: int) -> str:
        """Attempt to detect table title from surrounding text"""
        # This is a simplified implementation
        # In practice, you'd analyze text blocks above the table
        return f"Table {page_num + 1}"
```

---

## 2. Advanced Features Technical Specifications

### 2.1 Anti-Hallucination System Implementation

**System Architecture:**
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   User Query    │───▶│  Mode Selection  │───▶│  Parameter      │
│                 │    │  Engine          │    │  Injection      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Context        │    │  Citation        │    │  Response       │
│  Validation     │    │  Enforcement     │    │  Generation     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

**Mode-Specific Configurations:**

**Strict Mode (Temperature: 0.2)**
```python
STRICT_MODE_CONFIG = {
    "temperature": 0.2,
    "num_ctx": 4096,
    "num_predict": 1536,
    "top_p": 0.9,
    "top_k": 60,
    "repeat_penalty": 1.1,
    "confidence_threshold": 0.6,
    "system_prompt": """You are a precise research assistant for ERDB. Answer EXCLUSIVELY based on provided context. You MUST ALWAYS cite your sources using bracket notation [filename.pdf] for every factual claim. If information is not in the provided context, respond with "I cannot find information about this in the provided documents." Format scientific names in markdown italics (*Genus species*). Never make assumptions or use external knowledge.""",
    "citation_enforcement": "mandatory",
    "external_knowledge": "prohibited",
    "response_validation": "strict_context_only"
}
```

**Balanced Mode (Temperature: 0.25)**
```python
BALANCED_MODE_CONFIG = {
    "temperature": 0.25,
    "num_ctx": 4096,
    "num_predict": 1536,
    "top_p": 0.9,
    "top_k": 60,
    "repeat_penalty": 1.1,
    "confidence_threshold": 0.4,
    "system_prompt": """You are a knowledgeable assistant for ERDB. Answer primarily from context with careful inference when clearly labeled. You MUST ALWAYS cite your sources using bracket notation [filename.pdf]. When making inferences beyond the direct context, clearly indicate this with phrases like "Based on the context, this suggests..." Format scientific names in markdown italics (*Genus species*).""",
    "citation_enforcement": "mandatory",
    "external_knowledge": "limited_inference",
    "response_validation": "context_primary_with_inference"
}
```

**Off Mode (Temperature: 0.8)**
```python
OFF_MODE_CONFIG = {
    "temperature": 0.8,
    "num_ctx": 4096,
    "num_predict": 1280,
    "top_p": 0.95,
    "top_k": 80,
    "repeat_penalty": 1.0,
    "confidence_threshold": 0.0,
    "system_prompt": """You are an expert ERDB consultant. Use context as foundation, supplement with expertise when helpful. Cite sources using bracket notation [filename.pdf] when referencing documents. Clearly distinguish between document-based information and general expertise. Format scientific names in markdown italics (*Genus species*).""",
    "citation_enforcement": "recommended",
    "external_knowledge": "allowed",
    "response_validation": "context_foundation_with_expertise"
}
```

**Citation Enforcement Implementation:**
```python
import re
from typing import List, Dict, Tuple

class CitationEnforcer:
    def __init__(self, mode: str):
        self.mode = mode
        self.citation_pattern = r'\[([^\]]+\.pdf)\]'
        self.factual_claim_indicators = [
            r'\b(according to|shows that|indicates|demonstrates|found that|reports|states)\b',
            r'\b(\d+%|\d+\.\d+%|significant|increase|decrease|higher|lower)\b',
            r'\b(study|research|analysis|data|results|findings)\b'
        ]

    def validate_response(self, response: str, available_sources: List[str]) -> Dict[str, Any]:
        """Validate response against citation requirements"""
        citations = re.findall(self.citation_pattern, response)
        factual_claims = self._detect_factual_claims(response)

        validation_result = {
            "valid": True,
            "citations_found": len(citations),
            "factual_claims": len(factual_claims),
            "missing_citations": [],
            "invalid_citations": [],
            "warnings": []
        }

        # Check citation validity
        for citation in citations:
            if citation not in available_sources:
                validation_result["invalid_citations"].append(citation)
                validation_result["valid"] = False

        # Mode-specific validation
        if self.mode == "strict":
            if factual_claims and len(citations) == 0:
                validation_result["valid"] = False
                validation_result["warnings"].append("Factual claims require citations in strict mode")

        return validation_result

    def _detect_factual_claims(self, text: str) -> List[str]:
        """Detect sentences that likely contain factual claims"""
        sentences = text.split('.')
        factual_sentences = []

        for sentence in sentences:
            for pattern in self.factual_claim_indicators:
                if re.search(pattern, sentence, re.IGNORECASE):
                    factual_sentences.append(sentence.strip())
                    break

        return factual_sentences
```

### 2.2 Scientific Name Processing Technical Implementation

**Processing Pipeline Architecture:**
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Text Input    │───▶│  Microservice    │───▶│  CRAFT Model    │
│                 │    │  Health Check    │    │  Processing     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Regex Fallback │◄───│  Service Failed  │    │  Confidence     │
│  Processing     │    │  Detection       │    │  Scoring        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

**Main Application Integration:**
```python
import requests
import re
from typing import Dict, List, Optional, Tuple
import logging

class ScientificNameProcessor:
    def __init__(self, microservice_url: str = "http://localhost:5005"):
        self.microservice_url = microservice_url
        self.timeout = 5.0
        self.fallback_patterns = [
            # Binomial nomenclature
            r'\b[A-Z][a-z]+ [a-z]+\b',
            # Trinomial nomenclature
            r'\b[A-Z][a-z]+ [a-z]+ [a-z]+\b',
            # Abbreviated genus
            r'\b[A-Z]\. [a-z]+\b',
            # Species designation
            r'\b[A-Z][a-z]+ sp\.\b'
        ]
        self.exclusion_patterns = [
            r'\b(United States|New York|South Africa)\b',  # Geographic names
            r'\b(John Smith|Mary Johnson)\b',  # Common names
            r'\b(et al|in press|personal communication)\b'  # Academic terms
        ]

    def process_text(self, text: str, preserve_urls: bool = True) -> Dict[str, Any]:
        """Process text for scientific name detection and italicization"""
        try:
            # Try microservice first
            result = self._try_microservice(text, preserve_urls)
            if result["success"]:
                return result
        except Exception as e:
            logging.warning(f"Microservice failed: {e}")

        # Fallback to regex processing
        return self._regex_fallback(text, preserve_urls)

    def _try_microservice(self, text: str, preserve_urls: bool) -> Dict[str, Any]:
        """Attempt processing with ScispaCy microservice"""
        try:
            response = requests.post(
                f"{self.microservice_url}/detect_scientific_names",
                json={"text": text, "preserve_urls": preserve_urls},
                timeout=self.timeout
            )

            if response.status_code == 200:
                data = response.json()
                return {
                    "success": True,
                    "processed_text": data["processed_text"],
                    "scientific_names_found": data["scientific_names_found"],
                    "processing_method": "microservice",
                    "fallback_used": False
                }
        except requests.RequestException:
            pass

        return {"success": False}

    def _regex_fallback(self, text: str, preserve_urls: bool) -> Dict[str, Any]:
        """Fallback regex-based scientific name detection"""
        scientific_names = []
        processed_text = text

        if preserve_urls:
            # Preserve URLs during processing
            url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+'
            urls = re.findall(url_pattern, text)
            url_placeholders = {url: f"__URL_{i}__" for i, url in enumerate(urls)}

            for url, placeholder in url_placeholders.items():
                processed_text = processed_text.replace(url, placeholder)

        # Apply regex patterns
        for pattern in self.fallback_patterns:
            matches = re.finditer(pattern, processed_text)
            for match in matches:
                candidate = match.group()

                # Check exclusions
                if not any(re.search(excl, candidate, re.IGNORECASE)
                          for excl in self.exclusion_patterns):

                    # Validate as potential scientific name
                    if self._validate_scientific_name(candidate):
                        scientific_names.append({
                            "text": candidate,
                            "start": match.start(),
                            "end": match.end(),
                            "confidence": 0.7,  # Lower confidence for regex
                            "method": "regex_fallback"
                        })

        # Apply italicization
        processed_text = self._apply_italicization(processed_text, scientific_names)

        if preserve_urls:
            # Restore URLs
            for url, placeholder in url_placeholders.items():
                processed_text = processed_text.replace(placeholder, url)

        return {
            "success": True,
            "processed_text": processed_text,
            "scientific_names_found": scientific_names,
            "processing_method": "regex_fallback",
            "fallback_used": True
        }

    def _validate_scientific_name(self, candidate: str) -> bool:
        """Validate potential scientific name using heuristics"""
        words = candidate.split()

        # Basic validation rules
        if len(words) < 2:
            return False

        # First word should be capitalized genus
        if not words[0][0].isupper() or not words[0][1:].islower():
            return False

        # Second word should be lowercase species
        if not words[1].islower():
            return False

        # Check for common non-scientific patterns
        if any(word in ['and', 'or', 'the', 'of', 'in'] for word in words):
            return False

        return True

    def _apply_italicization(self, text: str, names: List[Dict]) -> str:
        """Apply markdown italicization to detected names"""
        # Sort by position (reverse to avoid offset issues)
        names_sorted = sorted(names, key=lambda x: x['start'], reverse=True)

        for name in names_sorted:
            original = name['text']
            italicized = f"*{original}*"
            text = text[:name['start']] + italicized + text[name['end']:]

        return text

    def health_check(self) -> Dict[str, Any]:
        """Check microservice health"""
        try:
            response = requests.get(
                f"{self.microservice_url}/health",
                timeout=self.timeout
            )

            if response.status_code == 200:
                return response.json()
        except requests.RequestException as e:
            return {
                "status": "error",
                "message": str(e),
                "microservice_available": False
            }

        return {
            "status": "error",
            "message": "Service unavailable",
            "microservice_available": False
        }

### 2.3 Gated Download System Technical Implementation

**Security Architecture:**
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Citation      │───▶│  Access Form     │───▶│  Token          │
│   Click         │    │  Submission      │    │  Generation     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Email          │    │  Audit Trail     │    │  Secure         │
│  Delivery       │    │  Logging         │    │  Download       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

**Token-Based Security Implementation:**
```python
import secrets
import hashlib
import jwt
from datetime import datetime, timedelta
from typing import Dict, Optional
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

class GatedDownloadManager:
    def __init__(self, secret_key: str, token_expiry_hours: int = 24):
        self.secret_key = secret_key
        self.token_expiry_hours = token_expiry_hours
        self.audit_logger = AuditLogger()

    def generate_download_token(self, filename: str, user_info: Dict[str, str]) -> Dict[str, Any]:
        """Generate secure download token with expiration"""
        token_data = {
            "filename": filename,
            "user_email": user_info["email"],
            "user_name": user_info["name"],
            "organization": user_info.get("organization", ""),
            "purpose": user_info["purpose"],
            "issued_at": datetime.utcnow().isoformat(),
            "expires_at": (datetime.utcnow() + timedelta(hours=self.token_expiry_hours)).isoformat(),
            "token_id": secrets.token_urlsafe(32)
        }

        # Create JWT token
        token = jwt.encode(token_data, self.secret_key, algorithm="HS256")

        # Log token generation
        self.audit_logger.log_token_generation(token_data)

        return {
            "token": token,
            "expires_at": token_data["expires_at"],
            "download_url": f"/secure_download/{token}",
            "token_id": token_data["token_id"]
        }

    def validate_download_token(self, token: str) -> Dict[str, Any]:
        """Validate download token and check expiration"""
        try:
            # Decode JWT token
            token_data = jwt.decode(token, self.secret_key, algorithms=["HS256"])

            # Check expiration
            expires_at = datetime.fromisoformat(token_data["expires_at"])
            if datetime.utcnow() > expires_at:
                return {
                    "valid": False,
                    "error": "Token expired",
                    "expired": True
                }

            # Log token usage
            self.audit_logger.log_token_usage(token_data)

            return {
                "valid": True,
                "filename": token_data["filename"],
                "user_info": {
                    "email": token_data["user_email"],
                    "name": token_data["user_name"],
                    "organization": token_data.get("organization", ""),
                    "purpose": token_data["purpose"]
                },
                "token_id": token_data["token_id"]
            }

        except jwt.ExpiredSignatureError:
            return {"valid": False, "error": "Token expired", "expired": True}
        except jwt.InvalidTokenError:
            return {"valid": False, "error": "Invalid token", "expired": False}

    def send_download_email(self, user_email: str, filename: str, token: str) -> bool:
        """Send secure download link via email"""
        try:
            # Email configuration
            smtp_server = os.getenv("SMTP_SERVER", "localhost")
            smtp_port = int(os.getenv("SMTP_PORT", "587"))
            smtp_username = os.getenv("SMTP_USERNAME")
            smtp_password = os.getenv("SMTP_PASSWORD")

            # Create email
            msg = MIMEMultipart()
            msg["From"] = os.getenv("FROM_EMAIL", "<EMAIL>")
            msg["To"] = user_email
            msg["Subject"] = f"Secure Download Link - {filename}"

            # Email body
            download_url = f"{os.getenv('BASE_URL', 'http://localhost:8080')}/secure_download/{token}"
            expires_at = (datetime.utcnow() + timedelta(hours=self.token_expiry_hours)).strftime("%B %d, %Y at %I:%M %p")

            body = f"""
Dear User,

Your request to access "{filename}" has been approved.

Download Link: {download_url}
Token Expires: {expires_at}

This link will expire in {self.token_expiry_hours} hours and can only be used once.

For security reasons, please do not share this link with others.

Best regards,
ERDB Document Management System
            """

            msg.attach(MIMEText(body, "plain"))

            # Send email
            with smtplib.SMTP(smtp_server, smtp_port) as server:
                if smtp_username and smtp_password:
                    server.starttls()
                    server.login(smtp_username, smtp_password)
                server.send_message(msg)

            return True

        except Exception as e:
            logging.error(f"Failed to send download email: {e}")
            return False

class AuditLogger:
    def __init__(self, log_file: str = "logs/download_audit.log"):
        self.log_file = log_file
        self.setup_logging()

    def setup_logging(self):
        """Setup audit logging configuration"""
        import logging

        # Create logs directory if it doesn't exist
        os.makedirs(os.path.dirname(self.log_file), exist_ok=True)

        # Configure audit logger
        self.logger = logging.getLogger("download_audit")
        self.logger.setLevel(logging.INFO)

        # File handler
        handler = logging.FileHandler(self.log_file)
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)

    def log_token_generation(self, token_data: Dict[str, str]):
        """Log token generation event"""
        self.logger.info(
            f"TOKEN_GENERATED - File: {token_data['filename']} - "
            f"User: {token_data['user_email']} - "
            f"Organization: {token_data.get('organization', 'N/A')} - "
            f"Purpose: {token_data['purpose']} - "
            f"Token ID: {token_data['token_id']}"
        )

    def log_token_usage(self, token_data: Dict[str, str]):
        """Log token usage event"""
        self.logger.info(
            f"TOKEN_USED - File: {token_data['filename']} - "
            f"User: {token_data['user_email']} - "
            f"Token ID: {token_data['token_id']}"
        )

    def log_download_completion(self, filename: str, user_email: str, token_id: str):
        """Log successful download completion"""
        self.logger.info(
            f"DOWNLOAD_COMPLETED - File: {filename} - "
            f"User: {user_email} - "
            f"Token ID: {token_id}"
        )

    def log_access_denied(self, reason: str, token_id: str = None):
        """Log access denied events"""
        self.logger.warning(
            f"ACCESS_DENIED - Reason: {reason} - "
            f"Token ID: {token_id or 'N/A'}"
        )
```

**Flask Route Implementation:**
```python
from flask import request, render_template, send_file, jsonify, redirect, url_for
import os

@app.route('/download_gated/<filename>')
def gated_download_form(filename):
    """Display gated download form"""
    # Verify file exists
    file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    if not os.path.exists(file_path):
        abort(404)

    return render_template('gated_download_form.html', filename=filename)

@app.route('/submit_download_form', methods=['POST'])
def submit_download_form():
    """Process download form submission"""
    try:
        data = request.get_json()
        filename = data['filename']
        user_info = {
            'name': data['name'],
            'email': data['email'],
            'organization': data.get('organization', ''),
            'purpose': data['purpose']
        }

        # Validate required fields
        if not all([filename, user_info['name'], user_info['email'], user_info['purpose']]):
            return jsonify({'error': 'Missing required fields'}), 400

        # Generate token
        gated_manager = GatedDownloadManager(app.config['SECRET_KEY'])
        token_info = gated_manager.generate_download_token(filename, user_info)

        # Send email
        email_sent = gated_manager.send_download_email(
            user_info['email'], filename, token_info['token']
        )

        if email_sent:
            return jsonify({
                'success': True,
                'message': 'Download link sent to your email',
                'token_expires': token_info['expires_at']
            })
        else:
            return jsonify({'error': 'Failed to send email'}), 500

    except Exception as e:
        logging.error(f"Download form submission error: {e}")
        return jsonify({'error': 'Internal server error'}), 500

@app.route('/secure_download/<token>')
def secure_download(token):
    """Handle secure file download with token validation"""
    try:
        gated_manager = GatedDownloadManager(app.config['SECRET_KEY'])
        validation_result = gated_manager.validate_download_token(token)

        if not validation_result['valid']:
            if validation_result.get('expired'):
                return render_template('download_expired.html'), 410
            else:
                return render_template('download_invalid.html'), 403

        filename = validation_result['filename']
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)

        if not os.path.exists(file_path):
            return render_template('file_not_found.html'), 404

        # Log successful download
        gated_manager.audit_logger.log_download_completion(
            filename,
            validation_result['user_info']['email'],
            validation_result['token_id']
        )

        # Send file
        return send_file(
            file_path,
            as_attachment=True,
            download_name=filename,
            mimetype='application/pdf'
        )

    except Exception as e:
        logging.error(f"Secure download error: {e}")
        return render_template('download_error.html'), 500

---

## 3. Configuration & Deployment Technical Guide

### 3.1 Model Parameter Optimization

**Production Configuration Template:**
```json
{
  "model_configurations": {
    "production_strict": {
      "llm_model": "hf.co/unsloth/gpt-oss-20b-GGUF:Q4_K_M",
      "embedding_model": "hf.co/nomic-ai/nomic-embed-text-v2-moe-GGUF:Q8_0",
      "vision_model": "gemma3:4b-it-q4_K_M",
      "parameters": {
        "temperature": 0.2,
        "num_ctx": 4096,
        "num_predict": 1536,
        "top_p": 0.9,
        "top_k": 60,
        "repeat_penalty": 1.1,
        "num_thread": 8,
        "num_gpu": 35
      },
      "performance_settings": {
        "batch_size": 512,
        "eval_batch_size": 512,
        "rope_frequency_base": 10000.0,
        "rope_frequency_scale": 1.0
      }
    },
    "development_balanced": {
      "llm_model": "llama3.1:8b-instruct-q4_K_M",
      "embedding_model": "mxbai-embed-large:latest",
      "vision_model": "gemma3:4b-it-q4_K_M",
      "parameters": {
        "temperature": 0.25,
        "num_ctx": 2048,
        "num_predict": 1024,
        "top_p": 0.9,
        "top_k": 60,
        "repeat_penalty": 1.1,
        "num_thread": 4,
        "num_gpu": 25
      }
    }
  }
}
```

**Dynamic Configuration Management:**
```python
import json
import os
from typing import Dict, Any
from dataclasses import dataclass

@dataclass
class ModelConfig:
    llm_model: str
    embedding_model: str
    vision_model: str
    temperature: float
    num_ctx: int
    num_predict: int
    top_p: float
    top_k: int
    repeat_penalty: float
    num_thread: int
    num_gpu: int

class ConfigurationManager:
    def __init__(self, config_path: str = "config/default_models.json"):
        self.config_path = config_path
        self.current_config = self.load_configuration()

    def load_configuration(self) -> Dict[str, Any]:
        """Load configuration from JSON file"""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            return self.get_default_configuration()

    def get_default_configuration(self) -> Dict[str, Any]:
        """Return default configuration if file not found"""
        return {
            "llm_model": "hf.co/unsloth/gpt-oss-20b-GGUF:Q4_K_M",
            "embedding_model": "hf.co/nomic-ai/nomic-embed-text-v2-moe-GGUF:Q8_0",
            "vision_model": "gemma3:4b-it-q4_K_M",
            "anti_hallucination_mode": "strict",
            "gpu_layer_offloading": {
                "enabled": True,
                "default_mode": "balanced"
            },
            "model_parameters": {
                "strict": {
                    "temperature": 0.2,
                    "num_ctx": 4096,
                    "num_predict": 1536,
                    "top_p": 0.9,
                    "top_k": 60,
                    "repeat_penalty": 1.1
                }
            }
        }

    def update_configuration(self, updates: Dict[str, Any]) -> bool:
        """Update configuration and save to file"""
        try:
            self.current_config.update(updates)
            with open(self.config_path, 'w') as f:
                json.dump(self.current_config, f, indent=2)
            return True
        except Exception as e:
            logging.error(f"Failed to update configuration: {e}")
            return False

    def get_model_config(self, mode: str = None) -> ModelConfig:
        """Get model configuration for specified mode"""
        if mode is None:
            mode = self.current_config.get("anti_hallucination_mode", "strict")

        base_config = self.current_config
        mode_params = base_config.get("model_parameters", {}).get(mode, {})

        return ModelConfig(
            llm_model=base_config.get("llm_model"),
            embedding_model=base_config.get("embedding_model"),
            vision_model=base_config.get("vision_model"),
            temperature=mode_params.get("temperature", 0.2),
            num_ctx=mode_params.get("num_ctx", 4096),
            num_predict=mode_params.get("num_predict", 1536),
            top_p=mode_params.get("top_p", 0.9),
            top_k=mode_params.get("top_k", 60),
            repeat_penalty=mode_params.get("repeat_penalty", 1.1),
            num_thread=mode_params.get("num_thread", 8),
            num_gpu=self._get_gpu_layers()
        )

    def _get_gpu_layers(self) -> int:
        """Calculate GPU layers based on offloading mode"""
        gpu_config = self.current_config.get("gpu_layer_offloading", {})
        if not gpu_config.get("enabled", True):
            return 0

        mode = gpu_config.get("default_mode", "balanced")
        layer_mapping = {
            "conservative": 25,
            "balanced": 35,
            "performance": 45,
            "disabled": 0
        }

        return layer_mapping.get(mode, 35)
```

### 3.2 GPU Layer Offloading Configuration

**Hardware Requirements by Mode:**
```yaml
gpu_configurations:
  conservative:
    min_vram: "4GB"
    recommended_vram: "6GB"
    num_gpu_layers: 25
    expected_performance: "70% of full GPU"
    memory_usage: "~4GB VRAM"
    cpu_fallback: "75% of layers on CPU"

  balanced:
    min_vram: "6GB"
    recommended_vram: "8GB"
    num_gpu_layers: 35
    expected_performance: "85% of full GPU"
    memory_usage: "~6GB VRAM"
    cpu_fallback: "65% of layers on CPU"

  performance:
    min_vram: "8GB"
    recommended_vram: "12GB"
    num_gpu_layers: 45
    expected_performance: "95% of full GPU"
    memory_usage: "~8GB VRAM"
    cpu_fallback: "55% of layers on CPU"

  disabled:
    min_vram: "12GB"
    recommended_vram: "16GB"
    num_gpu_layers: 0
    expected_performance: "100% GPU utilization"
    memory_usage: "~12GB+ VRAM"
    cpu_fallback: "0% (full GPU)"
```

**Dynamic GPU Configuration:**
```python
import subprocess
import psutil
import GPUtil
from typing import Dict, Optional, Tuple

class GPUOptimizer:
    def __init__(self):
        self.gpu_info = self.detect_gpu_configuration()

    def detect_gpu_configuration(self) -> Dict[str, Any]:
        """Detect available GPU resources"""
        try:
            gpus = GPUtil.getGPUs()
            if not gpus:
                return {"available": False, "reason": "No GPU detected"}

            gpu = gpus[0]  # Use first GPU
            return {
                "available": True,
                "name": gpu.name,
                "memory_total": gpu.memoryTotal,
                "memory_free": gpu.memoryFree,
                "memory_used": gpu.memoryUsed,
                "utilization": gpu.load * 100,
                "temperature": gpu.temperature
            }
        except Exception as e:
            return {"available": False, "reason": str(e)}

    def recommend_offloading_mode(self) -> str:
        """Recommend optimal GPU offloading mode based on available resources"""
        if not self.gpu_info.get("available"):
            return "disabled"

        memory_total = self.gpu_info.get("memory_total", 0)
        memory_free = self.gpu_info.get("memory_free", 0)

        if memory_free >= 12000:  # 12GB+
            return "disabled"  # Full GPU usage
        elif memory_free >= 8000:  # 8GB+
            return "performance"
        elif memory_free >= 6000:  # 6GB+
            return "balanced"
        elif memory_free >= 4000:  # 4GB+
            return "conservative"
        else:
            return "disabled"  # Force CPU-only

    def apply_gpu_optimization(self, mode: str) -> Dict[str, Any]:
        """Apply GPU optimization settings"""
        optimization_settings = {
            "conservative": {
                "num_gpu": 25,
                "batch_size": 256,
                "eval_batch_size": 256,
                "num_thread": psutil.cpu_count() // 2
            },
            "balanced": {
                "num_gpu": 35,
                "batch_size": 512,
                "eval_batch_size": 512,
                "num_thread": psutil.cpu_count() // 2
            },
            "performance": {
                "num_gpu": 45,
                "batch_size": 1024,
                "eval_batch_size": 1024,
                "num_thread": psutil.cpu_count()
            },
            "disabled": {
                "num_gpu": 0,
                "batch_size": 128,
                "eval_batch_size": 128,
                "num_thread": psutil.cpu_count()
            }
        }

        settings = optimization_settings.get(mode, optimization_settings["balanced"])

        # Set environment variables for Ollama
        os.environ["OLLAMA_NUM_GPU"] = str(settings["num_gpu"])
        os.environ["OLLAMA_NUM_THREAD"] = str(settings["num_thread"])

        return {
            "mode": mode,
            "settings": settings,
            "gpu_info": self.gpu_info,
            "applied": True
        }

    def monitor_gpu_usage(self) -> Dict[str, Any]:
        """Monitor real-time GPU usage"""
        try:
            gpus = GPUtil.getGPUs()
            if not gpus:
                return {"available": False}

            gpu = gpus[0]
            return {
                "available": True,
                "utilization": gpu.load * 100,
                "memory_used": gpu.memoryUsed,
                "memory_total": gpu.memoryTotal,
                "memory_percent": (gpu.memoryUsed / gpu.memoryTotal) * 100,
                "temperature": gpu.temperature,
                "timestamp": datetime.utcnow().isoformat()
            }
        except Exception as e:
            return {"available": False, "error": str(e)}

### 3.3 Scientific Name Microservice Deployment

**Production Deployment Architecture:**
```
┌─────────────────────────────────────────────────────────────────┐
│                    Production Environment                       │
├─────────────────────────────────────────────────────────────────┤
│  Main App (Python 3.8)  │  ScispaCy Service (Python 3.10)     │
│  Port: 8080             │  Port: 5005                          │
│  Process: gunicorn      │  Process: systemd service           │
└─────────────────────────────────────────────────────────────────┘
```

**Systemd Service Configuration:**
```ini
# /etc/systemd/system/scispacy-microservice.service
[Unit]
Description=ScispaCy Scientific Name Processing Microservice
After=network.target

[Service]
Type=simple
User=scispacy
Group=scispacy
WorkingDirectory=/opt/erdb-ai/
Environment=PATH=/opt/erdb-ai/venv_310/bin
ExecStart=/opt/erdb-ai/venv_310/bin/python scripts/venv310_scispacy_microservice.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=scispacy-microservice

# Resource limits
MemoryLimit=2G
CPUQuota=200%

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/erdb-ai/logs

[Install]
WantedBy=multi-user.target
```

**Docker Deployment Configuration:**
```dockerfile
# Dockerfile.scispacy
FROM python:3.10-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements
COPY requirements_scispacy.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements_scispacy.txt

# Install ScispaCy model
RUN pip install https://s3-us-west-2.amazonaws.com/ai2-s2-scispacy/releases/v0.5.1/en_ner_craft_md-0.5.1.tar.gz

# Copy application code
COPY scripts/venv310_scispacy_microservice.py .

# Create non-root user
RUN useradd -m -u 1000 scispacy
USER scispacy

# Expose port
EXPOSE 5005

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:5005/health || exit 1

# Start application
CMD ["python", "venv310_scispacy_microservice.py"]
```

**Docker Compose Integration:**
```yaml
# docker-compose.yml
version: '3.8'

services:
  main-app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SCI_NAME_MICROSERVICE_URL=http://scispacy-service:5005/detect_scientific_names
    depends_on:
      - scispacy-service
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs

  scispacy-service:
    build:
      context: .
      dockerfile: Dockerfile.scispacy
    ports:
      - "5005:5005"
    environment:
      - FLASK_ENV=production
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5005/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - main-app
```

**Health Monitoring Implementation:**
```python
import requests
import time
import logging
from typing import Dict, Any
from datetime import datetime, timedelta

class MicroserviceHealthMonitor:
    def __init__(self, service_url: str = "http://localhost:5005"):
        self.service_url = service_url
        self.health_endpoint = f"{service_url}/health"
        self.last_health_check = None
        self.consecutive_failures = 0
        self.max_failures = 3

    def check_health(self) -> Dict[str, Any]:
        """Perform health check on microservice"""
        try:
            response = requests.get(self.health_endpoint, timeout=5)

            if response.status_code == 200:
                health_data = response.json()
                self.consecutive_failures = 0
                self.last_health_check = datetime.utcnow()

                return {
                    "healthy": True,
                    "status": health_data.get("status"),
                    "model_loaded": health_data.get("model_loaded"),
                    "response_time": response.elapsed.total_seconds(),
                    "last_check": self.last_health_check.isoformat()
                }
            else:
                self.consecutive_failures += 1
                return {
                    "healthy": False,
                    "error": f"HTTP {response.status_code}",
                    "consecutive_failures": self.consecutive_failures
                }

        except requests.RequestException as e:
            self.consecutive_failures += 1
            return {
                "healthy": False,
                "error": str(e),
                "consecutive_failures": self.consecutive_failures
            }

    def is_service_available(self) -> bool:
        """Quick availability check"""
        health_result = self.check_health()
        return health_result.get("healthy", False)

    def should_use_fallback(self) -> bool:
        """Determine if fallback should be used"""
        return self.consecutive_failures >= self.max_failures

    def get_service_metrics(self) -> Dict[str, Any]:
        """Get comprehensive service metrics"""
        health_result = self.check_health()

        return {
            "service_url": self.service_url,
            "health_status": health_result,
            "uptime_check": self.last_health_check,
            "failure_count": self.consecutive_failures,
            "fallback_recommended": self.should_use_fallback(),
            "timestamp": datetime.utcnow().isoformat()
        }
```

### 3.4 Environment Variables & Production Configuration

**Complete Environment Configuration:**
```bash
# Production .env file template

# ============================================================================
# CORE APPLICATION SETTINGS
# ============================================================================
FLASK_ENV=production
FLASK_DEBUG=false
SECRET_KEY=your-super-secure-secret-key-here
FLASK_SECRET_KEY=your-flask-specific-secret-key

# ============================================================================
# AI MODEL CONFIGURATION
# ============================================================================
# Primary models (optimized for production)
LLM_MODEL=hf.co/unsloth/gpt-oss-20b-GGUF:Q4_K_M
TEXT_EMBEDDING_MODEL=hf.co/nomic-ai/nomic-embed-text-v2-moe-GGUF:Q8_0
VISION_MODEL=gemma3:4b-it-q4_K_M

# Ollama service configuration
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_API_HOST=http://localhost:11434
OLLAMA_DISABLE_TELEMETRY=1

# Anti-hallucination system
ANTI_HALLUCINATION_MODE=strict
CONFIDENCE_THRESHOLD=0.6

# ============================================================================
# GPU OPTIMIZATION SETTINGS
# ============================================================================
ENABLE_PERFORMANCE_OPTIMIZATION=true
GPU_LAYER_OFFLOADING_MODE=balanced
OLLAMA_NUM_GPU=35
OLLAMA_NUM_THREAD=8

# ============================================================================
# SCIENTIFIC NAME PROCESSING
# ============================================================================
SCI_NAME_MICROSERVICE_URL=http://localhost:5005/detect_scientific_names
SCI_NAME_FALLBACK_ENABLED=true
SCI_NAME_TIMEOUT=5.0

# ============================================================================
# DATABASE CONFIGURATION
# ============================================================================
# ChromaDB settings
CHROMA_PATH=./data/unified_chroma
CHROMA_COLLECTION_NAME=erdb_documents

# SQLite databases
CHAT_DB_PATH=./data/chat_history.db
SCRAPED_DB_PATH=./data/scraped_pages.db
USER_DB_PATH=./data/user_management.db

# ============================================================================
# FILE PROCESSING SETTINGS
# ============================================================================
TEMP_FOLDER=./data/_temp
MAX_CONTENT_LENGTH=26214400  # 25MB in bytes
UPLOAD_FOLDER=./data/uploads

# PDF processing
GHOSTSCRIPT_PATH=/usr/bin/gs
EXTRACT_TABLES=true
TABLE_STRATEGY=advanced
MAX_TABLES_DISPLAY=3

# Image processing
USE_VISION_MODEL=false
USE_VISION_MODEL_DURING_EMBEDDING=false
VISION_CACHE_ENABLED=true
FILTER_PDF_IMAGES=true
PDF_IMAGE_FILTER_SENSITIVITY=medium
MAX_PDF_IMAGES_TO_ANALYZE=30

# ============================================================================
# GATED DOWNLOAD SYSTEM
# ============================================================================
ENABLE_GATED_DOWNLOADS=true
DOWNLOAD_TOKEN_EXPIRY_HOURS=24
BASE_URL=https://your-domain.com

# Email configuration for download notifications
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>

# ============================================================================
# SECURITY SETTINGS
# ============================================================================
# Rate limiting
RATELIMIT_STORAGE_URL=redis://localhost:6379/0
RATELIMIT_DEFAULT=100 per hour
RATELIMIT_CHAT=20 per minute
RATELIMIT_UPLOAD=5 per minute

# CSRF protection
WTF_CSRF_ENABLED=true
WTF_CSRF_TIME_LIMIT=3600

# Session security
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=Lax

# ============================================================================
# LOGGING & MONITORING
# ============================================================================
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log
AUDIT_LOG_FILE=./logs/audit.log
DOWNLOAD_AUDIT_LOG=./logs/download_audit.log

# Performance monitoring
ENABLE_METRICS=true
METRICS_PORT=9090

# ============================================================================
# QUERY PROCESSING CONFIGURATION
# ============================================================================
# Retrieval settings
RETRIEVAL_K=12
RELEVANCE_THRESHOLD=0.15
MIN_DOCUMENTS=3
MAX_DOCUMENTS=8

# MMR settings
USE_MMR=true
MMR_LAMBDA=0.5
MMR_FETCH_K=48

# Temporal/recency settings
INCLUDE_TEMPORAL_SUMMARY=true
PREFER_RECENT_WHEN_CONFLICT=true
RECENCY_BIAS_WEIGHT=0.0

# ============================================================================
# BACKUP & MAINTENANCE
# ============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_LOCATION=./backups

# Database maintenance
AUTO_VACUUM_ENABLED=true
VACUUM_SCHEDULE=0 3 * * 0  # Weekly on Sunday at 3 AM
```

**Configuration Validation Script:**
```python
import os
import sys
from typing import Dict, List, Any
import requests

class ConfigurationValidator:
    def __init__(self):
        self.required_vars = [
            'SECRET_KEY', 'FLASK_SECRET_KEY', 'LLM_MODEL',
            'TEXT_EMBEDDING_MODEL', 'CHROMA_PATH'
        ]
        self.optional_vars = [
            'SCI_NAME_MICROSERVICE_URL', 'SMTP_SERVER', 'BACKUP_ENABLED'
        ]

    def validate_environment(self) -> Dict[str, Any]:
        """Validate all environment variables and dependencies"""
        results = {
            "valid": True,
            "errors": [],
            "warnings": [],
            "services": {}
        }

        # Check required environment variables
        for var in self.required_vars:
            if not os.getenv(var):
                results["errors"].append(f"Missing required environment variable: {var}")
                results["valid"] = False

        # Check optional variables
        for var in self.optional_vars:
            if not os.getenv(var):
                results["warnings"].append(f"Optional environment variable not set: {var}")

        # Validate service connections
        results["services"]["ollama"] = self._check_ollama_service()
        results["services"]["scispacy"] = self._check_scispacy_service()
        results["services"]["database"] = self._check_database_paths()

        return results

    def _check_ollama_service(self) -> Dict[str, Any]:
        """Check Ollama service availability"""
        try:
            ollama_url = os.getenv('OLLAMA_BASE_URL', 'http://localhost:11434')
            response = requests.get(f"{ollama_url}/api/tags", timeout=5)

            if response.status_code == 200:
                models = response.json().get('models', [])
                return {
                    "available": True,
                    "models_count": len(models),
                    "url": ollama_url
                }
        except Exception as e:
            return {
                "available": False,
                "error": str(e),
                "url": ollama_url
            }

    def _check_scispacy_service(self) -> Dict[str, Any]:
        """Check ScispaCy microservice availability"""
        service_url = os.getenv('SCI_NAME_MICROSERVICE_URL')
        if not service_url:
            return {"available": False, "reason": "URL not configured"}

        try:
            health_url = service_url.replace('/detect_scientific_names', '/health')
            response = requests.get(health_url, timeout=5)

            if response.status_code == 200:
                return {
                    "available": True,
                    "health_data": response.json(),
                    "url": service_url
                }
        except Exception as e:
            return {
                "available": False,
                "error": str(e),
                "url": service_url
            }

    def _check_database_paths(self) -> Dict[str, Any]:
        """Check database directory permissions and paths"""
        paths_to_check = [
            os.getenv('CHROMA_PATH', './data/unified_chroma'),
            os.getenv('TEMP_FOLDER', './data/_temp'),
            os.path.dirname(os.getenv('CHAT_DB_PATH', './data/chat_history.db'))
        ]

        results = {"accessible": True, "paths": {}}

        for path in paths_to_check:
            try:
                os.makedirs(path, exist_ok=True)
                # Test write permissions
                test_file = os.path.join(path, '.write_test')
                with open(test_file, 'w') as f:
                    f.write('test')
                os.remove(test_file)

                results["paths"][path] = {"accessible": True}
            except Exception as e:
                results["accessible"] = False
                results["paths"][path] = {"accessible": False, "error": str(e)}

        return results

if __name__ == "__main__":
    validator = ConfigurationValidator()
    results = validator.validate_environment()

    print("Configuration Validation Results:")
    print("=" * 50)

    if results["valid"]:
        print("✅ Configuration is valid")
    else:
        print("❌ Configuration has errors")
        for error in results["errors"]:
            print(f"  ERROR: {error}")

    if results["warnings"]:
        print("\nWarnings:")
        for warning in results["warnings"]:
            print(f"  WARNING: {warning}")

    print("\nService Status:")
    for service, status in results["services"].items():
        if status.get("available"):
            print(f"  ✅ {service}: Available")
        else:
            print(f"  ❌ {service}: {status.get('error', 'Not available')}")

    sys.exit(0 if results["valid"] else 1)

---

## 4. Complete API Reference & Developer Integration

### 4.1 REST API Endpoints Specification

**Base URL Structure:**
```
Production: https://your-domain.com/api/v1
Development: http://localhost:8080/api/v1
```

**Authentication & Headers:**
```http
Content-Type: application/json
Authorization: Bearer <session_token>
X-CSRF-Token: <csrf_token>
User-Agent: ERDB-Client/1.0
```

### 4.2 Core Chat Interface API

**Endpoint:** `POST /chat`

**Request Schema:**
```json
{
  "message": "string (required, max 2000 chars)",
  "category": "string (optional, default: 'all')",
  "user_name": "string (required, max 100 chars)",
  "anti_hallucination_mode": "string (optional, enum: ['strict', 'balanced', 'off'])",
  "include_tables": "boolean (optional, default: true)",
  "include_images": "boolean (optional, default: true)",
  "max_sources": "integer (optional, default: 8, max: 12)",
  "temperature_override": "float (optional, range: 0.1-1.0)",
  "context_window": "integer (optional, default: 4096, max: 8192)"
}
```

**Response Schema:**
```json
{
  "response": "string (markdown formatted)",
  "sources": [
    {
      "filename": "string",
      "page": "integer",
      "excerpt": "string (max 500 chars)",
      "relevance_score": "float (0.0-1.0)",
      "gated_download_url": "string (optional)",
      "source_url": "string (optional)",
      "publication_date": "string (ISO 8601, optional)"
    }
  ],
  "tables": [
    {
      "table_id": "string",
      "title": "string",
      "html": "string (Bootstrap styled)",
      "data": "array of arrays",
      "source": "string (filename)",
      "page": "integer",
      "extraction_method": "string"
    }
  ],
  "scientific_names_detected": ["string"],
  "follow_up_questions": ["string"],
  "processing_metrics": {
    "response_time": "float (seconds)",
    "tokens_used": "integer",
    "sources_retrieved": "integer",
    "tables_found": "integer",
    "model_parameters": {
      "temperature": "float",
      "top_p": "float",
      "max_tokens": "integer",
      "mode": "string"
    }
  },
  "timestamp": "string (ISO 8601)"
}
```

**Error Response Schema:**
```json
{
  "error": true,
  "error_code": "string",
  "message": "string",
  "details": {
    "field": "string (optional)",
    "validation_errors": ["string"]
  },
  "timestamp": "string (ISO 8601)",
  "request_id": "string (UUID)"
}
```

**Implementation Example:**
```python
from flask import request, jsonify, session
from typing import Dict, Any
import uuid
import time

@app.route('/api/v1/chat', methods=['POST'])
@require_authentication
@rate_limit('20 per minute')
def chat_api():
    """Enhanced chat API with full feature support"""
    request_id = str(uuid.uuid4())
    start_time = time.time()

    try:
        # Validate request
        data = request.get_json()
        validation_result = validate_chat_request(data)
        if not validation_result['valid']:
            return jsonify({
                'error': True,
                'error_code': 'VALIDATION_ERROR',
                'message': 'Invalid request data',
                'details': validation_result['errors'],
                'request_id': request_id
            }), 400

        # Extract parameters
        message = data['message']
        category = data.get('category', 'all')
        user_name = data['user_name']
        anti_hallucination_mode = data.get('anti_hallucination_mode', 'strict')

        # Process query with enhanced features
        query_processor = EnhancedQueryProcessor(
            mode=anti_hallucination_mode,
            include_tables=data.get('include_tables', True),
            include_scientific_names=True,
            max_sources=data.get('max_sources', 8)
        )

        result = query_processor.process_query(
            message=message,
            category=category,
            user_name=user_name,
            temperature_override=data.get('temperature_override'),
            context_window=data.get('context_window', 4096)
        )

        # Calculate processing metrics
        processing_time = time.time() - start_time

        response = {
            'response': result['response'],
            'sources': result['sources'],
            'tables': result.get('tables', []),
            'scientific_names_detected': result.get('scientific_names', []),
            'follow_up_questions': result.get('follow_up_questions', []),
            'processing_metrics': {
                'response_time': round(processing_time, 3),
                'tokens_used': result.get('tokens_used', 0),
                'sources_retrieved': len(result['sources']),
                'tables_found': len(result.get('tables', [])),
                'model_parameters': result.get('model_parameters', {})
            },
            'timestamp': datetime.utcnow().isoformat(),
            'request_id': request_id
        }

        # Log successful request
        log_api_request(request_id, 'chat', user_name, processing_time, True)

        return jsonify(response)

    except Exception as e:
        # Log error
        log_api_request(request_id, 'chat', data.get('user_name'),
                       time.time() - start_time, False, str(e))

        return jsonify({
            'error': True,
            'error_code': 'INTERNAL_ERROR',
            'message': 'An internal error occurred',
            'request_id': request_id,
            'timestamp': datetime.utcnow().isoformat()
        }), 500

def validate_chat_request(data: Dict[str, Any]) -> Dict[str, Any]:
    """Validate chat request data"""
    errors = []

    # Required fields
    if not data.get('message'):
        errors.append('Message is required')
    elif len(data['message']) > 2000:
        errors.append('Message too long (max 2000 characters)')

    if not data.get('user_name'):
        errors.append('User name is required')
    elif len(data['user_name']) > 100:
        errors.append('User name too long (max 100 characters)')

    # Optional field validation
    if 'anti_hallucination_mode' in data:
        if data['anti_hallucination_mode'] not in ['strict', 'balanced', 'off']:
            errors.append('Invalid anti-hallucination mode')

    if 'temperature_override' in data:
        temp = data['temperature_override']
        if not isinstance(temp, (int, float)) or temp < 0.1 or temp > 1.0:
            errors.append('Temperature must be between 0.1 and 1.0')

    if 'max_sources' in data:
        max_sources = data['max_sources']
        if not isinstance(max_sources, int) or max_sources < 1 or max_sources > 12:
            errors.append('Max sources must be between 1 and 12')

    return {
        'valid': len(errors) == 0,
        'errors': errors
    }
```

### 4.3 Document Upload API

**Endpoint:** `POST /api/v1/documents/upload`

**Request (multipart/form-data):**
```
file: File (required, PDF, max 25MB)
category: string (required)
title: string (optional)
description: string (optional)
publication_date: string (optional, ISO 8601)
enable_gated_download: boolean (optional, default: false)
source_url: string (optional, URL)
extract_tables: boolean (optional, default: true)
process_scientific_names: boolean (optional, default: true)
use_vision_model: boolean (optional, default: false)
```

**Response Schema:**
```json
{
  "success": true,
  "document_id": "string (UUID)",
  "filename": "string",
  "processing_results": {
    "pages_processed": "integer",
    "chunks_created": "integer",
    "tables_extracted": "integer",
    "scientific_names_found": "integer",
    "images_processed": "integer",
    "processing_time": "float (seconds)"
  },
  "metadata": {
    "category": "string",
    "title": "string",
    "publication_date": "string (ISO 8601, optional)",
    "file_size": "integer (bytes)",
    "page_count": "integer"
  },
  "features_enabled": {
    "gated_download": "boolean",
    "table_extraction": "boolean",
    "scientific_name_processing": "boolean",
    "vision_analysis": "boolean"
  },
  "timestamp": "string (ISO 8601)"
}
```

### 4.4 Scientific Name Processing API

**Endpoint:** `POST /api/v1/text/process-scientific-names`

**Request Schema:**
```json
{
  "text": "string (required, max 10000 chars)",
  "preserve_urls": "boolean (optional, default: true)",
  "confidence_threshold": "float (optional, default: 0.7)",
  "return_positions": "boolean (optional, default: false)"
}
```

**Response Schema:**
```json
{
  "processed_text": "string (with italicized scientific names)",
  "scientific_names_found": [
    {
      "original": "string",
      "formatted": "string (italicized)",
      "confidence": "float (0.0-1.0)",
      "method": "string (scispacy|regex_fallback)",
      "type": "string (binomial|trinomial|abbreviated)",
      "start_position": "integer (optional)",
      "end_position": "integer (optional)"
    }
  ],
  "processing_method": "string (microservice|regex_fallback)",
  "microservice_available": "boolean",
  "processing_time": "float (seconds)",
  "timestamp": "string (ISO 8601)"
}
```

### 4.5 Gated Download API

**Endpoint:** `POST /api/v1/downloads/request-access`

**Request Schema:**
```json
{
  "filename": "string (required)",
  "user_info": {
    "name": "string (required, max 100 chars)",
    "email": "string (required, valid email)",
    "organization": "string (optional, max 200 chars)",
    "purpose": "string (required, max 500 chars)"
  },
  "terms_accepted": "boolean (required, must be true)"
}
```

**Response Schema:**
```json
{
  "success": true,
  "message": "string",
  "token_info": {
    "expires_at": "string (ISO 8601)",
    "valid_for_hours": "integer"
  },
  "email_sent": "boolean",
  "audit_id": "string (UUID)"
}
```

**Endpoint:** `GET /api/v1/downloads/secure/<token>`

**Response:** File download or error page

### 4.6 System Health & Monitoring API

**Endpoint:** `GET /api/v1/system/health`

**Response Schema:**
```json
{
  "status": "string (healthy|degraded|unhealthy)",
  "timestamp": "string (ISO 8601)",
  "components": {
    "main_application": {
      "status": "string",
      "uptime": "float (seconds)",
      "memory_usage": "float (MB)",
      "cpu_usage": "float (percentage)"
    },
    "ollama_service": {
      "status": "string",
      "models_loaded": "integer",
      "response_time": "float (seconds)"
    },
    "chromadb": {
      "status": "string",
      "documents_indexed": "integer",
      "collection_size": "integer"
    },
    "scispacy_microservice": {
      "status": "string",
      "model_loaded": "boolean",
      "response_time": "float (seconds)"
    },
    "gpu_optimization": {
      "enabled": "boolean",
      "mode": "string",
      "gpu_utilization": "float (percentage, optional)",
      "vram_usage": "float (MB, optional)"
    }
  },
  "performance_metrics": {
    "average_response_time": "float (seconds)",
    "requests_per_minute": "float",
    "error_rate": "float (percentage)",
    "cache_hit_rate": "float (percentage)"
  }
}

---

## 5. Technical Troubleshooting & System Maintenance

### 5.1 Common Issues & Diagnostic Procedures

#### 5.1.1 Ollama Connection Issues

**Symptoms:**
- "Connection refused" errors
- Model loading failures
- Slow response times

**Diagnostic Commands:**
```bash
# Check Ollama service status
systemctl status ollama
curl -f http://localhost:11434/api/tags

# Check GPU availability
nvidia-smi
ollama ps

# Monitor resource usage
htop
watch -n 1 'nvidia-smi --query-gpu=utilization.gpu,memory.used,memory.total --format=csv'
```

**Resolution Steps:**
```bash
# Restart Ollama service
sudo systemctl restart ollama

# Check GPU memory and clear if needed
ollama stop <model_name>
ollama run <model_name>

# Verify model integrity
ollama pull <model_name>

# Check disk space
df -h
du -sh ~/.ollama/
```

**Configuration Fix:**
```python
# app/services/ollama_service.py
class OllamaHealthChecker:
    def __init__(self, base_url: str = "http://localhost:11434"):
        self.base_url = base_url
        self.timeout = 10

    def diagnose_connection_issues(self) -> Dict[str, Any]:
        """Comprehensive Ollama diagnostics"""
        diagnostics = {
            "service_reachable": False,
            "models_available": False,
            "gpu_accessible": False,
            "memory_sufficient": False,
            "recommendations": []
        }

        try:
            # Test basic connectivity
            response = requests.get(f"{self.base_url}/api/tags", timeout=self.timeout)
            if response.status_code == 200:
                diagnostics["service_reachable"] = True
                models = response.json().get('models', [])
                diagnostics["models_available"] = len(models) > 0

                if not models:
                    diagnostics["recommendations"].append("No models loaded. Run: ollama pull <model_name>")

        except requests.ConnectionError:
            diagnostics["recommendations"].append("Ollama service not running. Run: systemctl start ollama")
        except requests.Timeout:
            diagnostics["recommendations"].append("Ollama service slow to respond. Check system resources")

        # Check GPU availability
        try:
            gpu_response = requests.post(
                f"{self.base_url}/api/generate",
                json={"model": "llama3.1:8b", "prompt": "test", "stream": False},
                timeout=5
            )
            if gpu_response.status_code == 200:
                diagnostics["gpu_accessible"] = True
        except:
            diagnostics["recommendations"].append("GPU not accessible. Check CUDA installation")

        return diagnostics
```

#### 5.1.2 ScispaCy Microservice Failures

**Symptoms:**
- Scientific names not being italicized
- Microservice health check failures
- Fallback to regex processing

**Diagnostic Procedure:**
```bash
# Check microservice status
curl -f http://localhost:5005/health
systemctl status scispacy-microservice

# Check Python 3.10 environment
source venv_310/bin/activate
python --version
pip list | grep scispacy

# Test model loading
python -c "import spacy; nlp = spacy.load('en_ner_craft_md'); print('Model loaded successfully')"
```

**Resolution Steps:**
```bash
# Restart microservice
sudo systemctl restart scispacy-microservice

# Reinstall ScispaCy model if corrupted
source venv_310/bin/activate
pip uninstall en-ner-craft-md
pip install https://s3-us-west-2.amazonaws.com/ai2-s2-scispacy/releases/v0.5.1/en_ner_craft_md-0.5.1.tar.gz

# Check port availability
netstat -tlnp | grep 5005
lsof -i :5005
```

**Automated Recovery Script:**
```python
#!/usr/bin/env python3
# scripts/recover_scispacy_service.py

import subprocess
import requests
import time
import logging
from typing import Dict, Any

class ScispaCyRecoveryManager:
    def __init__(self):
        self.service_url = "http://localhost:5005"
        self.max_recovery_attempts = 3

    def attempt_recovery(self) -> Dict[str, Any]:
        """Attempt automated recovery of ScispaCy service"""
        recovery_log = {
            "attempts": [],
            "success": False,
            "final_status": None
        }

        for attempt in range(self.max_recovery_attempts):
            attempt_log = {
                "attempt_number": attempt + 1,
                "actions_taken": [],
                "success": False
            }

            # Step 1: Check if service is responding
            if self._check_service_health():
                attempt_log["success"] = True
                recovery_log["success"] = True
                break

            # Step 2: Restart systemd service
            restart_result = self._restart_systemd_service()
            attempt_log["actions_taken"].append(f"Systemd restart: {restart_result}")

            # Wait and check again
            time.sleep(10)
            if self._check_service_health():
                attempt_log["success"] = True
                recovery_log["success"] = True
                break

            # Step 3: Manual process restart
            if attempt == 1:  # Second attempt
                manual_restart = self._manual_restart()
                attempt_log["actions_taken"].append(f"Manual restart: {manual_restart}")

            # Step 4: Model reinstallation (last resort)
            if attempt == 2:  # Third attempt
                model_reinstall = self._reinstall_model()
                attempt_log["actions_taken"].append(f"Model reinstall: {model_reinstall}")

            recovery_log["attempts"].append(attempt_log)
            time.sleep(5)

        recovery_log["final_status"] = self._get_service_status()
        return recovery_log

    def _check_service_health(self) -> bool:
        """Check if ScispaCy service is healthy"""
        try:
            response = requests.get(f"{self.service_url}/health", timeout=5)
            return response.status_code == 200 and response.json().get("model_loaded", False)
        except:
            return False

    def _restart_systemd_service(self) -> str:
        """Restart systemd service"""
        try:
            result = subprocess.run(
                ["sudo", "systemctl", "restart", "scispacy-microservice"],
                capture_output=True, text=True, timeout=30
            )
            return "success" if result.returncode == 0 else f"failed: {result.stderr}"
        except Exception as e:
            return f"error: {str(e)}"

    def _manual_restart(self) -> str:
        """Manual process restart"""
        try:
            # Kill existing processes
            subprocess.run(["pkill", "-f", "venv310_scispacy_microservice.py"],
                         capture_output=True)
            time.sleep(2)

            # Start new process
            subprocess.Popen([
                "/opt/erdb-ai/venv_310/bin/python",
                "/opt/erdb-ai/scripts/venv310_scispacy_microservice.py"
            ])

            return "success"
        except Exception as e:
            return f"error: {str(e)}"

    def _reinstall_model(self) -> str:
        """Reinstall ScispaCy model"""
        try:
            # Activate virtual environment and reinstall
            commands = [
                "source /opt/erdb-ai/venv_310/bin/activate",
                "pip uninstall -y en-ner-craft-md",
                "pip install https://s3-us-west-2.amazonaws.com/ai2-s2-scispacy/releases/v0.5.1/en_ner_craft_md-0.5.1.tar.gz"
            ]

            result = subprocess.run(
                " && ".join(commands),
                shell=True, capture_output=True, text=True, timeout=300
            )

            return "success" if result.returncode == 0 else f"failed: {result.stderr}"
        except Exception as e:
            return f"error: {str(e)}"

    def _get_service_status(self) -> Dict[str, Any]:
        """Get comprehensive service status"""
        try:
            response = requests.get(f"{self.service_url}/health", timeout=5)
            if response.status_code == 200:
                return response.json()
        except:
            pass

        return {"status": "error", "model_loaded": False}

if __name__ == "__main__":
    recovery_manager = ScispaCyRecoveryManager()
    result = recovery_manager.attempt_recovery()

    if result["success"]:
        print("✅ ScispaCy service recovery successful")
        exit(0)
    else:
        print("❌ ScispaCy service recovery failed")
        print(f"Recovery log: {result}")
        exit(1)
```

#### 5.1.3 GPU Memory Issues

**Symptoms:**
- CUDA out of memory errors
- Model loading failures
- Slow inference times

**Diagnostic Commands:**
```bash
# Monitor GPU memory usage
watch -n 1 nvidia-smi

# Check GPU processes
nvidia-smi pmon

# Monitor Ollama GPU usage
ollama ps
```

**Memory Optimization Script:**
```python
# scripts/gpu_memory_optimizer.py
import subprocess
import json
import GPUtil
from typing import Dict, Any, List

class GPUMemoryOptimizer:
    def __init__(self):
        self.gpu_info = self._get_gpu_info()

    def optimize_memory_usage(self) -> Dict[str, Any]:
        """Optimize GPU memory usage based on available resources"""
        optimization_result = {
            "current_usage": self.gpu_info,
            "recommendations": [],
            "actions_taken": [],
            "new_configuration": {}
        }

        if not self.gpu_info.get("available"):
            optimization_result["recommendations"].append("No GPU available - using CPU only")
            return optimization_result

        memory_free = self.gpu_info.get("memory_free", 0)
        memory_total = self.gpu_info.get("memory_total", 0)
        memory_usage_percent = ((memory_total - memory_free) / memory_total) * 100

        # Determine optimal configuration
        if memory_usage_percent > 90:
            # Critical memory usage
            new_mode = "conservative"
            optimization_result["recommendations"].append("Critical GPU memory usage - switching to conservative mode")
        elif memory_usage_percent > 75:
            # High memory usage
            new_mode = "balanced"
            optimization_result["recommendations"].append("High GPU memory usage - switching to balanced mode")
        elif memory_free > 8000:
            # Plenty of memory available
            new_mode = "performance"
            optimization_result["recommendations"].append("Sufficient GPU memory - using performance mode")
        else:
            new_mode = "balanced"

        # Apply optimization
        config_update = self._apply_gpu_optimization(new_mode)
        optimization_result["actions_taken"].append(f"Applied {new_mode} GPU configuration")
        optimization_result["new_configuration"] = config_update

        return optimization_result

    def _get_gpu_info(self) -> Dict[str, Any]:
        """Get current GPU information"""
        try:
            gpus = GPUtil.getGPUs()
            if not gpus:
                return {"available": False}

            gpu = gpus[0]
            return {
                "available": True,
                "name": gpu.name,
                "memory_total": gpu.memoryTotal,
                "memory_free": gpu.memoryFree,
                "memory_used": gpu.memoryUsed,
                "utilization": gpu.load * 100,
                "temperature": gpu.temperature
            }
        except Exception as e:
            return {"available": False, "error": str(e)}

    def _apply_gpu_optimization(self, mode: str) -> Dict[str, Any]:
        """Apply GPU optimization configuration"""
        configurations = {
            "conservative": {"num_gpu": 25, "batch_size": 256},
            "balanced": {"num_gpu": 35, "batch_size": 512},
            "performance": {"num_gpu": 45, "batch_size": 1024}
        }

        config = configurations.get(mode, configurations["balanced"])

        # Update environment variables
        import os
        os.environ["OLLAMA_NUM_GPU"] = str(config["num_gpu"])

        # Update configuration file
        config_path = "config/default_models.json"
        try:
            with open(config_path, 'r') as f:
                current_config = json.load(f)

            current_config["gpu_layer_offloading"]["default_mode"] = mode

            with open(config_path, 'w') as f:
                json.dump(current_config, f, indent=2)

            return {"mode": mode, "config": config, "updated": True}
        except Exception as e:
            return {"mode": mode, "config": config, "updated": False, "error": str(e)}
```

### 5.2 System Health Monitoring

**Comprehensive Health Check Script:**
```python
# scripts/system_health_monitor.py
import requests
import psutil
import sqlite3
import os
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List

class SystemHealthMonitor:
    def __init__(self):
        self.health_checks = {
            "ollama": self._check_ollama_health,
            "scispacy": self._check_scispacy_health,
            "database": self._check_database_health,
            "disk_space": self._check_disk_space,
            "memory": self._check_memory_usage,
            "gpu": self._check_gpu_health
        }

    def run_comprehensive_health_check(self) -> Dict[str, Any]:
        """Run all health checks and return comprehensive status"""
        health_report = {
            "timestamp": datetime.utcnow().isoformat(),
            "overall_status": "healthy",
            "components": {},
            "alerts": [],
            "recommendations": []
        }

        for component, check_func in self.health_checks.items():
            try:
                result = check_func()
                health_report["components"][component] = result

                # Check for issues
                if not result.get("healthy", True):
                    health_report["overall_status"] = "degraded"
                    health_report["alerts"].append(f"{component}: {result.get('message', 'Unknown issue')}")

                # Add recommendations
                if result.get("recommendations"):
                    health_report["recommendations"].extend(result["recommendations"])

            except Exception as e:
                health_report["components"][component] = {
                    "healthy": False,
                    "error": str(e),
                    "message": f"Health check failed for {component}"
                }
                health_report["overall_status"] = "unhealthy"
                health_report["alerts"].append(f"{component}: Health check failed - {str(e)}")

        return health_report

    def _check_ollama_health(self) -> Dict[str, Any]:
        """Check Ollama service health"""
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get('models', [])
                return {
                    "healthy": True,
                    "models_loaded": len(models),
                    "response_time": response.elapsed.total_seconds()
                }
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "recommendations": ["Check if Ollama service is running: systemctl status ollama"]
            }

    def _check_scispacy_health(self) -> Dict[str, Any]:
        """Check ScispaCy microservice health"""
        try:
            response = requests.get("http://localhost:5005/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                return {
                    "healthy": data.get("model_loaded", False),
                    "status": data.get("status"),
                    "response_time": response.elapsed.total_seconds()
                }
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e),
                "recommendations": ["Check ScispaCy microservice: systemctl status scispacy-microservice"]
            }

    def _check_database_health(self) -> Dict[str, Any]:
        """Check database health and integrity"""
        databases = [
            "data/chat_history.db",
            "data/user_management.db",
            "data/scraped_pages.db"
        ]

        db_status = {"healthy": True, "databases": {}}

        for db_path in databases:
            try:
                if os.path.exists(db_path):
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    cursor.execute("PRAGMA integrity_check")
                    result = cursor.fetchone()[0]
                    conn.close()

                    db_status["databases"][db_path] = {
                        "exists": True,
                        "integrity": result == "ok",
                        "size_mb": round(os.path.getsize(db_path) / 1024 / 1024, 2)
                    }

                    if result != "ok":
                        db_status["healthy"] = False
                else:
                    db_status["databases"][db_path] = {"exists": False}

            except Exception as e:
                db_status["healthy"] = False
                db_status["databases"][db_path] = {"error": str(e)}

        return db_status

    def _check_disk_space(self) -> Dict[str, Any]:
        """Check disk space usage"""
        disk_usage = psutil.disk_usage('/')
        free_gb = disk_usage.free / (1024**3)
        total_gb = disk_usage.total / (1024**3)
        used_percent = (disk_usage.used / disk_usage.total) * 100

        status = {
            "healthy": used_percent < 90,
            "free_gb": round(free_gb, 2),
            "total_gb": round(total_gb, 2),
            "used_percent": round(used_percent, 2)
        }

        if used_percent > 95:
            status["recommendations"] = ["Critical: Disk space very low - clean up files immediately"]
        elif used_percent > 85:
            status["recommendations"] = ["Warning: Disk space low - consider cleanup"]

        return status

    def _check_memory_usage(self) -> Dict[str, Any]:
        """Check system memory usage"""
        memory = psutil.virtual_memory()

        return {
            "healthy": memory.percent < 90,
            "total_gb": round(memory.total / (1024**3), 2),
            "available_gb": round(memory.available / (1024**3), 2),
            "used_percent": memory.percent,
            "recommendations": ["Consider restarting services if memory usage is high"] if memory.percent > 85 else []
        }

    def _check_gpu_health(self) -> Dict[str, Any]:
        """Check GPU health and utilization"""
        try:
            import GPUtil
            gpus = GPUtil.getGPUs()

            if not gpus:
                return {"healthy": True, "available": False, "message": "No GPU detected"}

            gpu = gpus[0]
            memory_percent = (gpu.memoryUsed / gpu.memoryTotal) * 100

            status = {
                "healthy": gpu.temperature < 85 and memory_percent < 95,
                "available": True,
                "name": gpu.name,
                "temperature": gpu.temperature,
                "memory_used_percent": round(memory_percent, 2),
                "utilization": round(gpu.load * 100, 2)
            }

            if gpu.temperature > 80:
                status["recommendations"] = ["GPU temperature high - check cooling"]
            if memory_percent > 90:
                status["recommendations"] = status.get("recommendations", []) + ["GPU memory usage high"]

            return status

        except ImportError:
            return {"healthy": True, "available": False, "message": "GPUtil not available"}
        except Exception as e:
            return {"healthy": False, "error": str(e)}

if __name__ == "__main__":
    monitor = SystemHealthMonitor()
    health_report = monitor.run_comprehensive_health_check()

    print(f"System Health Report - {health_report['timestamp']}")
    print(f"Overall Status: {health_report['overall_status'].upper()}")

    if health_report['alerts']:
        print("\nAlerts:")
        for alert in health_report['alerts']:
            print(f"  ⚠️  {alert}")

    if health_report['recommendations']:
        print("\nRecommendations:")
        for rec in health_report['recommendations']:
            print(f"  💡 {rec}")

    print(f"\nComponent Status:")
    for component, status in health_report['components'].items():
        health_icon = "✅" if status.get('healthy', True) else "❌"
        print(f"  {health_icon} {component}")
```

This completes the comprehensive technical reference documentation for the ERDB AI-Powered Knowledge Management Platform. The documentation now covers all aspects requested:

1. **System Architecture & Components** - Detailed technical specifications
2. **Advanced Features Technical Specifications** - Implementation details for all optimized features
3. **Configuration & Deployment Technical Guide** - Production-ready configuration examples
4. **Complete API Reference & Developer Integration** - Full API documentation with schemas
5. **Technical Troubleshooting & System Maintenance** - Comprehensive diagnostic and recovery procedures

The documentation is specifically targeted for DevOps engineers, backend developers, system administrators, and technical support staff, providing them with all the technical details needed to deploy, maintain, and troubleshoot the system effectively.
```
```
```
```
```
